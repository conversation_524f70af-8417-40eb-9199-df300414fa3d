/*
 * Test file to verify our common headers work
 * This file tests basic compilation of our fixed headers
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <string>
#include <vector>

// Test function to verify compilation
void test_function() {
    // Test basic types
    DWORD testDword = 0;
    BYTE testByte = 0;

    // Test class instantiation
    CAsyncLogInfo logInfo;
    logInfo.SetLogType(ASYNC_LOG_INFO);
    logInfo.SetLogMessage("Test message");

    // Test STL usage
    std::vector<int> testVector;
    std::string testString = "Hello World";

    // Test basic operations
    testVector.push_back(42);
    DWORD logType = logInfo.GetLogType();
}

// Test class implementation
void CAsyncLogInfo::Initialize() {
    m_dwLogType = ASYNC_LOG_NONE;
    m_dwLogID = 0;
    m_strLogMessage = "";
    GetSystemTime(&m_LogTime);
}

void CAsyncLogInfo::Cleanup() {
    m_strLogMessage.clear();
    m_dwLogType = ASYNC_LOG_NONE;
    m_dwLogID = 0;
}

